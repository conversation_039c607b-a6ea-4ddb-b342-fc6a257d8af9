# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/*
Dockerfile.cross

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Go workspace file
go.work

# Kubernetes Generated files - skip generated files, except for vendored files
!vendor/**/zz_generated.*

# editor and IDE paraphernalia
.idea
.vscode
*.swp
*.swo
*~

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# macOS 生成的一些其他系统文件
# .DS_Store 文件夹
.DS_Store
# .DS_Store 文件及其所有变体
.DS_Store?
# Finder 创建的文件
._*
# Spotlight 索引文件
.Spotlight-V100
# 垃圾桶文件
.Trashes
# Windows 系统文件（在跨平台开发）
ehthumbs.db
Thumbs.db
