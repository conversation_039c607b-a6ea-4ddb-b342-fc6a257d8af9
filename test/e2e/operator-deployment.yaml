apiVersion: v1
kind: Namespace
metadata:
  name: etcd-operator-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
  labels:
    app.kubernetes.io/name: etcd-operator
    app.kubernetes.io/instance: controller-manager
    app.kubernetes.io/component: manager
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: etcd-operator
      app.kubernetes.io/instance: controller-manager
      app.kubernetes.io/component: manager
  replicas: 1
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        app.kubernetes.io/name: etcd-operator
        app.kubernetes.io/instance: controller-manager
        app.kubernetes.io/component: manager
    spec:
      containers:
      - command:
        - /manager
        args:
        - --leader-elect
        - --health-probe-bind-address=:8081
        - --metrics-bind-address=127.0.0.1:8080
        image: etcd-operator:e2e-test
        imagePullPolicy: Never
        name: manager
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - "ALL"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
      serviceAccountName: etcd-operator-controller-manager
      terminationGracePeriodSeconds: 10
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: etcd-operator-leader-election-role
  namespace: etcd-operator-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: etcd-operator-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - services
  - persistentvolumeclaims
  - pods
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters
  - etcdbackups
  - etcdrestores
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters/finalizers
  - etcdbackups/finalizers
  - etcdrestores/finalizers
  verbs:
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters/status
  - etcdbackups/status
  - etcdrestores/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: etcd-operator-leader-election-rolebinding
  namespace: etcd-operator-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: etcd-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: etcd-operator-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: etcd-operator-manager-role
subjects:
- kind: ServiceAccount
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
