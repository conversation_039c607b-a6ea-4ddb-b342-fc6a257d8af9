warning: no packages being tested depend on matches for pattern ./internal/controller/...
=== RUN   TestClusterService_SetDefaults
=== RUN   TestClusterService_SetDefaults/新集群设置默认Size
=== RUN   TestClusterService_SetDefaults/现有集群保持Size=0
=== RUN   TestClusterService_SetDefaults/现有集群保持自定义Size
--- PASS: TestClusterService_SetDefaults (0.00s)
    --- PASS: TestClusterService_SetDefaults/新集群设置默认Size (0.00s)
    --- PASS: TestClusterService_SetDefaults/现有集群保持Size=0 (0.00s)
    --- PASS: TestClusterService_SetDefaults/现有集群保持自定义Size (0.00s)
=== RUN   TestClusterService_ValidateClusterSpec
=== RUN   TestClusterService_ValidateClusterSpec/有效的单节点集群
=== RUN   TestClusterService_ValidateClusterSpec/有效的三节点集群
=== RUN   TestClusterService_ValidateClusterSpec/有效的五节点集群
=== RUN   TestClusterService_ValidateClusterSpec/无效的负数Size
=== RUN   TestClusterService_ValidateClusterSpec/无效的偶数Size
=== RUN   TestClusterService_ValidateClusterSpec/无效的偶数Size=4
=== RUN   TestClusterService_ValidateClusterSpec/无效的空版本
--- PASS: TestClusterService_ValidateClusterSpec (0.00s)
    --- PASS: TestClusterService_ValidateClusterSpec/有效的单节点集群 (0.00s)
    --- PASS: TestClusterService_ValidateClusterSpec/有效的三节点集群 (0.00s)
    --- PASS: TestClusterService_ValidateClusterSpec/有效的五节点集群 (0.00s)
    --- PASS: TestClusterService_ValidateClusterSpec/无效的负数Size (0.00s)
    --- PASS: TestClusterService_ValidateClusterSpec/无效的偶数Size (0.00s)
    --- PASS: TestClusterService_ValidateClusterSpec/无效的偶数Size=4 (0.00s)
    --- PASS: TestClusterService_ValidateClusterSpec/无效的空版本 (0.00s)
=== RUN   TestClusterService_InitializeCluster
=== RUN   TestClusterService_InitializeCluster/成功初始化新集群
=== RUN   TestClusterService_InitializeCluster/初始化时状态更新失败
--- PASS: TestClusterService_InitializeCluster (0.00s)
    --- PASS: TestClusterService_InitializeCluster/成功初始化新集群 (0.00s)
    --- PASS: TestClusterService_InitializeCluster/初始化时状态更新失败 (0.00s)
=== RUN   TestClusterService_CreateCluster
=== RUN   TestClusterService_CreateCluster/成功创建单节点集群
=== RUN   TestClusterService_CreateCluster/资源创建失败
=== RUN   TestClusterService_CreateCluster/多节点集群使用渐进式创建
--- PASS: TestClusterService_CreateCluster (0.00s)
    --- PASS: TestClusterService_CreateCluster/成功创建单节点集群 (0.00s)
    --- PASS: TestClusterService_CreateCluster/资源创建失败 (0.00s)
    --- PASS: TestClusterService_CreateCluster/多节点集群使用渐进式创建 (0.00s)
=== RUN   TestClusterService_IsClusterReady
=== RUN   TestClusterService_IsClusterReady/集群完全就绪
=== RUN   TestClusterService_IsClusterReady/集群部分就绪
=== RUN   TestClusterService_IsClusterReady/获取状态失败
--- PASS: TestClusterService_IsClusterReady (0.00s)
    --- PASS: TestClusterService_IsClusterReady/集群完全就绪 (0.00s)
    --- PASS: TestClusterService_IsClusterReady/集群部分就绪 (0.00s)
    --- PASS: TestClusterService_IsClusterReady/获取状态失败 (0.00s)
=== RUN   TestStatefulSetManager_GetStatus
=== RUN   TestStatefulSetManager_GetStatus/获取正常StatefulSet状态
=== RUN   TestStatefulSetManager_GetStatus/StatefulSet不存在时返回零值状态
--- PASS: TestStatefulSetManager_GetStatus (0.00s)
    --- PASS: TestStatefulSetManager_GetStatus/获取正常StatefulSet状态 (0.00s)
    --- PASS: TestStatefulSetManager_GetStatus/StatefulSet不存在时返回零值状态 (0.00s)
PASS
coverage: 24.2% of statements in ./pkg/service/..., ./pkg/resource/..., ./internal/controller/...
ok  	command-line-arguments	0.385s	coverage: 24.2% of statements in ./pkg/service/..., ./pkg/resource/..., ./internal/controller/...
