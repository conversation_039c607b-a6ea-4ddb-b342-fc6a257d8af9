=== RUN   TestIntegrationSuite
=== RUN   TestIntegrationSuite/TestClusterLifecycle
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-lifecycle","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Adding finalizer to EtcdCluster	{"etcdcluster": {"name":"test-lifecycle","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-lifecycle","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Initializing EtcdCluster
2025-08-09T23:49:26+08:00	INFO	Cluster initialization completed, transitioning to creating phase
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-lifecycle","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Creating EtcdCluster
2025-08-09T23:49:26+08:00	INFO	Cluster is not ready yet, requeuing
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-lifecycle","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Creating EtcdCluster
2025-08-09T23:49:26+08:00	INFO	Single-node cluster is ready, transitioning to running phase
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-lifecycle","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	EtcdCluster is being deleted	{"etcdcluster": {"name":"test-lifecycle","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Starting cluster deletion
2025-08-09T23:49:26+08:00	INFO	Cluster deletion completed
    integration_test.go:185: ✅ 集群生命周期测试完成
=== RUN   TestIntegrationSuite/TestClusterScaling
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-scaling","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Adding finalizer to EtcdCluster	{"etcdcluster": {"name":"test-scaling","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-scaling","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Initializing EtcdCluster
2025-08-09T23:49:26+08:00	INFO	Cluster initialization completed, transitioning to creating phase
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-scaling","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Creating EtcdCluster
2025-08-09T23:49:26+08:00	INFO	Multi-node cluster creation not yet implemented
    integration_test.go:272: ✅ 集群扩缩容测试完成
=== RUN   TestIntegrationSuite/TestMultiNodeClusterCreation
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-multinode","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Adding finalizer to EtcdCluster	{"etcdcluster": {"name":"test-multinode","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-multinode","namespace":"integration-test"}}
2025-08-09T23:49:26+08:00	INFO	Initializing EtcdCluster
2025-08-09T23:49:26+08:00	INFO	Cluster initialization completed, transitioning to creating phase
    integration_test.go:220: 📝 多节点集群创建功能还在开发中，跳过StatefulSet验证
    integration_test.go:222: ✅ 多节点集群创建测试完成
--- PASS: TestIntegrationSuite (0.01s)
    --- PASS: TestIntegrationSuite/TestClusterLifecycle (0.00s)
    --- PASS: TestIntegrationSuite/TestClusterScaling (0.00s)
    --- PASS: TestIntegrationSuite/TestMultiNodeClusterCreation (0.00s)
=== RUN   TestSimpleClusterReconcile
    simple_test.go:88: 🔄 执行第一次Reconcile (添加Finalizer)
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-simple","namespace":"default"}}
2025-08-09T23:49:26+08:00	INFO	Adding finalizer to EtcdCluster	{"etcdcluster": {"name":"test-simple","namespace":"default"}}
    simple_test.go:99: 🔄 执行第二次Reconcile (初始化集群)
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-simple","namespace":"default"}}
2025-08-09T23:49:26+08:00	INFO	Initializing EtcdCluster
2025-08-09T23:49:26+08:00	INFO	Cluster initialization completed, transitioning to creating phase
    simple_test.go:107: 📊 当前集群状态: Creating
    simple_test.go:112: 🔄 执行第三次Reconcile (创建资源)
2025-08-09T23:49:26+08:00	INFO	Starting reconciliation	{"etcdcluster": {"name":"test-simple","namespace":"default"}}
2025-08-09T23:49:26+08:00	INFO	Creating EtcdCluster
2025-08-09T23:49:26+08:00	INFO	Cluster is not ready yet, requeuing
    simple_test.go:116: ✅ 简化集成测试完成
--- PASS: TestSimpleClusterReconcile (0.00s)
=== RUN   TestFakeClientBasics
    simple_test.go:176: ✅ Fake客户端基础功能测试完成
--- PASS: TestFakeClientBasics (0.00s)
PASS
ok  	command-line-arguments	0.437s
