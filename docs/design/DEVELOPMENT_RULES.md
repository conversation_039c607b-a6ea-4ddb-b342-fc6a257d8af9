# ETCD Operator 开发规则

## 📋 概述

本文档定义了 ETCD Kubernetes Operator 项目的开发规则和最佳实践，确保代码质量、开发效率和项目的长期可维护性。

## 🎯 核心开发原则

### 1. 测试驱动开发 (TDD)

**规则**: 所有功能开发必须遵循测试驱动开发模式

**流程**:
```
1. 编写测试用例 → 2. 运行测试(失败) → 3. 编写最小实现 → 4. 运行测试(通过) → 5. 重构优化
```

**具体要求**:
- ✅ **先写测试**: 在编写功能代码之前，必须先编写对应的测试用例
- ✅ **测试覆盖**: 单元测试覆盖率不低于 80%，核心逻辑覆盖率不低于 95%
- ✅ **测试分层**: 单元测试 → 集成测试 → 端到端测试，每层都必须通过
- ✅ **测试先行**: 任何 PR 都必须包含相应的测试用例

### 2. 单模块开发原则

**规则**: 每次只专注开发一个模块，不允许同时开发多个模块

**模块定义**:
- **控制器模块**: EtcdCluster、EtcdBackup、EtcdRestore 控制器
- **功能模块**: TLS 安全、健康检查、扩缩容、备份恢复
- **工具模块**: 资源构建器、工具函数、常量定义
- **测试模块**: 单元测试、集成测试、端到端测试

**开发流程**:
```
选择模块 → 设计接口 → 编写测试 → 实现功能 → 运行测试 → 文档更新 → 代码审查 → 合并代码
```

**禁止行为**:
- ❌ 同时修改多个控制器
- ❌ 在功能未完成时开始新功能
- ❌ 跨模块的大规模重构

### 3. 质量保证原则

**规则**: 每个模块开发完成后必须执行完整测试，确保代码质量

**质量标准**:
- ✅ **编译通过**: `make build` 无错误
- ✅ **测试通过**: `make test-all` 全部通过
- ✅ **代码规范**: `go fmt` 和 `go vet` 检查通过
- ✅ **静态分析**: `golangci-lint` 检查通过
- ✅ **文档同步**: 相关文档已更新

## 🔄 开发工作流

### 阶段 1: 模块规划
1. **需求分析**: 明确模块功能和接口
2. **设计文档**: 编写技术设计文档
3. **测试计划**: 制定测试策略和用例
4. **时间估算**: 评估开发和测试时间

### 阶段 2: 测试先行
1. **编写测试**: 根据需求编写测试用例
2. **测试框架**: 设置测试环境和工具
3. **验证失败**: 确保测试在功能未实现时失败
4. **测试评审**: 团队评审测试用例的完整性

### 阶段 3: 功能实现
1. **最小实现**: 编写能通过测试的最小代码
2. **迭代开发**: 逐步完善功能实现
3. **持续测试**: 每次修改后运行测试
4. **代码审查**: 自我审查代码质量

### 阶段 4: 质量验证
1. **单元测试**: 运行模块的单元测试
2. **集成测试**: 验证模块间的协作
3. **端到端测试**: 验证完整的用户场景
4. **性能测试**: 检查性能和资源使用

### 阶段 5: 文档和交付
1. **文档更新**: 更新相关技术文档
2. **示例代码**: 提供使用示例
3. **变更日志**: 记录功能变更
4. **代码合并**: 提交 PR 并合并代码

## 📏 代码规范

### 1. Go 代码规范
- 遵循 [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- 使用 `gofmt` 格式化代码
- 使用 `go vet` 检查代码问题
- 遵循 [Effective Go](https://golang.org/doc/effective_go.html) 指南

### 2. 项目结构规范
```
etcd-k8s-operator/
├── api/v1alpha1/           # CRD 类型定义
├── internal/controller/    # 控制器实现
├── pkg/                    # 可复用的业务逻辑
├── test/                   # 测试代码
├── config/                 # Kubernetes 配置
├── docs/                   # 项目文档
└── scripts/                # 自动化脚本
```

### 3. 命名规范
- **包名**: 小写，简短，有意义 (`pkg/k8s`, `pkg/utils`)
- **函数名**: 驼峰命名，动词开头 (`BuildStatefulSet`, `ValidateCluster`)
- **变量名**: 驼峰命名，名词 (`clusterName`, `etcdVersion`)
- **常量名**: 大写，下划线分隔 (`DEFAULT_ETCD_VERSION`)

### 4. 注释规范
- **包注释**: 每个包都要有包级别的注释
- **函数注释**: 公开函数必须有注释，说明功能和参数
- **复杂逻辑**: 复杂的业务逻辑必须有详细注释
- **TODO 标记**: 临时代码用 TODO 标记，包含负责人和时间

## 🧪 测试规范

### 1. 测试分层
- **单元测试**: 测试单个函数或方法
- **集成测试**: 测试组件间的协作
- **端到端测试**: 测试完整的用户场景

### 2. 测试命名
- **测试文件**: `*_test.go`
- **测试函数**: `TestFunctionName`
- **测试套件**: `TestSuiteName`
- **基准测试**: `BenchmarkFunctionName`

### 3. 测试覆盖率
- **单元测试**: ≥ 80% 覆盖率
- **核心逻辑**: ≥ 95% 覆盖率
- **边界条件**: 必须测试边界和异常情况
- **回归测试**: 每个 Bug 修复都要有对应的测试

### 4. 测试数据
- **测试隔离**: 每个测试用例相互独立
- **数据清理**: 测试后自动清理测试数据
- **Mock 使用**: 外部依赖使用 Mock 对象
- **测试环境**: 使用专门的测试环境

## 📚 文档规范

### 1. 文档类型
- **README.md**: 项目概述和快速开始
- **技术文档**: API 设计、架构说明
- **开发文档**: 开发指南、测试指南
- **用户文档**: 使用手册、故障排除

### 2. 文档更新
- **同步更新**: 代码变更时同步更新文档
- **版本控制**: 文档版本与代码版本保持一致
- **审查机制**: 文档变更需要团队审查
- **定期检查**: 定期检查文档的准确性

### 3. 文档质量
- **准确性**: 文档内容必须与实际代码一致
- **完整性**: 覆盖所有重要的功能和用法
- **可读性**: 使用清晰的语言和结构
- **示例代码**: 提供实际可运行的示例

## 🔍 代码审查

### 1. 审查清单
- [ ] 功能是否符合需求
- [ ] 代码是否遵循规范
- [ ] 测试是否充分
- [ ] 文档是否更新
- [ ] 性能是否可接受
- [ ] 安全性是否考虑

### 2. 审查流程
1. **自我审查**: 提交前自我检查
2. **同行审查**: 至少一个同事审查
3. **测试验证**: 在审查环境中测试
4. **文档检查**: 验证文档的准确性

## 🚫 禁止行为

### 1. 开发禁忌
- ❌ **跳过测试**: 不允许跳过任何测试步骤
- ❌ **多模块并行**: 不允许同时开发多个模块
- ❌ **未测试提交**: 不允许提交未经测试的代码
- ❌ **文档滞后**: 不允许代码与文档不同步

### 2. 代码禁忌
- ❌ **硬编码**: 避免硬编码配置和常量
- ❌ **全局变量**: 避免使用全局变量
- ❌ **深度嵌套**: 避免过深的条件嵌套
- ❌ **长函数**: 单个函数不超过 50 行

### 3. 测试禁忌
- ❌ **测试依赖**: 测试用例之间不能有依赖
- ❌ **外部依赖**: 单元测试不能依赖外部服务
- ❌ **随机数据**: 避免使用随机数据影响测试稳定性
- ❌ **忽略失败**: 不能忽略或跳过失败的测试

## 📊 质量指标

### 1. 代码质量指标
- **测试覆盖率**: ≥ 80%
- **代码重复率**: ≤ 5%
- **圈复杂度**: ≤ 10
- **函数长度**: ≤ 50 行

### 2. 开发效率指标
- **构建时间**: ≤ 2 分钟
- **测试时间**: ≤ 15 分钟
- **部署时间**: ≤ 5 分钟
- **问题修复时间**: ≤ 1 天

### 3. 项目健康指标
- **Bug 密度**: ≤ 1 个/KLOC
- **技术债务**: ≤ 10%
- **文档覆盖率**: ≥ 90%
- **代码审查覆盖率**: 100%

## 🎯 执行和监督

### 1. 规则执行
- **强制性**: 所有开发人员必须遵循这些规则
- **自动化**: 通过 CI/CD 自动检查规则遵循情况
- **人工审查**: 代码审查时检查规则遵循情况
- **持续改进**: 根据实践经验持续改进规则

### 2. 违规处理
- **警告**: 首次违规给予警告和指导
- **培训**: 重复违规安排额外培训
- **审查**: 严重违规需要额外的代码审查
- **改进**: 分析违规原因并改进流程

### 3. 规则更新
- **定期评估**: 每月评估规则的有效性
- **团队讨论**: 重大变更需要团队讨论
- **版本控制**: 规则变更需要版本控制
- **通知机制**: 规则更新及时通知所有成员

---

**文档版本**: v1.0  
**最后更新**: 2025-07-24  
**下次评估**: 2025-08-24  
**维护者**: ETCD Operator 开发团队

**记住**: 这些规则的目的是提高代码质量和开发效率，而不是限制创新。如果规则阻碍了合理的开发需求，请及时提出讨论和改进建议。
