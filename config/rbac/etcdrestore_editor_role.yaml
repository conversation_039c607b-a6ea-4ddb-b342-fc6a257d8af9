# permissions for end users to edit etcdrestores.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: etcd-k8s-operator
    app.kubernetes.io/managed-by: kustomize
  name: etcdrestore-editor-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores/status
  verbs:
  - get
