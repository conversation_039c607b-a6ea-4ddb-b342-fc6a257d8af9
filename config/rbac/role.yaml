---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - persistentvolumeclaims
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups/finalizers
  verbs:
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters/finalizers
  verbs:
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores/finalizers
  verbs:
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores/status
  verbs:
  - get
  - patch
  - update
