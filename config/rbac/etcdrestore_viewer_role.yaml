# permissions for end users to view etcdrestores.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: etcd-k8s-operator
    app.kubernetes.io/managed-by: kustomize
  name: etcdrestore-viewer-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores/status
  verbs:
  - get
