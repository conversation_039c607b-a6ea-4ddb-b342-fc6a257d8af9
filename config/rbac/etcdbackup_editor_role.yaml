# permissions for end users to edit etcdbackups.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: etcd-k8s-operator
    app.kubernetes.io/managed-by: kustomize
  name: etcdbackup-editor-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups/status
  verbs:
  - get
